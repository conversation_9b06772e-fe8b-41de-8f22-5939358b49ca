import CryptoJS from "crypto-js";
import WebApp from "@twa-dev/sdk";

export interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
  photo_url?: string;
}

export interface TelegramWebAppInitData {
  user?: TelegramUser;
  chat_instance?: string;
  chat_type?: string;
  auth_date: number;
  hash: string;
}

export interface ValidationResult {
  isValid: boolean;
  user?: TelegramUser;
  error?: string;
}

/**
 * Validates Telegram Web App init data using the bot token
 * Based on Telegram's validation algorithm
 */
export function validateTelegramWebAppData(
  initData: string,
  botToken: string
): ValidationResult {
  try {
    // Parse the init data
    const urlParams = new URLSearchParams(initData);
    const hash = urlParams.get("hash");

    if (!hash) {
      return { isValid: false, error: "Hash is missing" };
    }

    // Remove hash from params for validation
    urlParams.delete("hash");

    // Sort parameters alphabetically and create data check string
    const dataCheckArray: string[] = [];
    for (const [key, value] of Array.from(urlParams.entries()).sort((a, b) =>
      a[0].localeCompare(b[0])
    )) {
      dataCheckArray.push(`${key}=${value}`);
    }
    const dataCheckString = dataCheckArray.join("\n");

    // Create secret key using bot token
    const secretKey = CryptoJS.HmacSHA256(botToken, "WebAppData");

    // Calculate expected hash
    const expectedHash = CryptoJS.HmacSHA256(
      dataCheckString,
      secretKey
    ).toString();

    // Verify hash
    if (hash !== expectedHash) {
      return { isValid: false, error: "Invalid hash" };
    }

    // Check auth_date (should not be older than 24 hours)
    const authDate = urlParams.get("auth_date");
    if (!authDate) {
      return { isValid: false, error: "Auth date is missing" };
    }

    const authTimestamp = parseInt(authDate, 10);
    const currentTimestamp = Math.floor(Date.now() / 1000);
    const maxAge = 24 * 60 * 60; // 24 hours in seconds

    if (currentTimestamp - authTimestamp > maxAge) {
      return { isValid: false, error: "Auth data is too old" };
    }

    // Parse user data
    const userParam = urlParams.get("user");
    if (!userParam) {
      return { isValid: false, error: "User data is missing" };
    }

    const user: TelegramUser = JSON.parse(userParam);

    return { isValid: true, user };
  } catch (error) {
    return {
      isValid: false,
      error: `Validation error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

/**
 * Extracts user information from Telegram Web App using @twa-dev/sdk
 * This should be called on the client side
 */
export function getTelegramWebAppData(): TelegramWebAppInitData | null {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    console.log("=== Getting Telegram WebApp Data using @twa-dev/sdk ===");

    let initData = "";

    // Primary method: Use @twa-dev/sdk
    try {
      console.log("WebApp SDK object:", WebApp);
      console.log("- initData:", WebApp.initData);
      console.log("- initDataUnsafe:", WebApp.initDataUnsafe);
      console.log("- platform:", WebApp.platform);
      console.log("- version:", WebApp.version);

      // Get initData from SDK
      initData = WebApp.initData || "";

      console.log("SDK initData result:", initData);
    } catch (sdkError) {
      console.warn("@twa-dev/sdk access failed:", sdkError);
    }

    // Fallback method: Check URL parameters for tgWebAppData
    if (!initData) {
      console.log("No SDK initData found, checking URL parameters...");
      const urlParams = new URLSearchParams(window.location.search);
      const tgWebAppData =
        urlParams.get("tgWebAppData") || urlParams.get("initData");
      if (tgWebAppData) {
        console.log("Found tgWebAppData in URL:", tgWebAppData);
        initData = tgWebAppData;
      }
    }

    // Another fallback: Check if data is in hash fragment
    if (!initData && window.location.hash) {
      console.log("Checking hash fragment for Telegram data...");
      const hashParams = new URLSearchParams(window.location.hash.substring(1));
      const hashInitData =
        hashParams.get("tgWebAppData") || hashParams.get("initData");
      if (hashInitData) {
        console.log("Found initData in hash:", hashInitData);
        initData = hashInitData;
      }
    }

    console.log("Final initData result:", initData);

    if (!initData) {
      console.warn("Telegram WebApp initData is empty");
      console.warn("WebApp SDK object:", WebApp);
      console.warn("Current URL:", window.location.href);
      console.warn("URL search params:", window.location.search);
      console.warn("URL hash:", window.location.hash);

      // For development/testing, check if we should create mock data
      if (
        process.env.NODE_ENV === "development" ||
        window.location.hostname === "localhost"
      ) {
        console.log(
          "Development mode detected - you can use the mock authentication component"
        );
      }

      return null;
    }

    // Parse init data from URL parameters
    const urlParams = new URLSearchParams(initData);
    const userParam = urlParams.get("user");
    const authDate = urlParams.get("auth_date");
    const hash = urlParams.get("hash");

    console.log("Parsed URL params:", {
      userParam: userParam ? "present" : "missing",
      authDate: authDate ? "present" : "missing",
      hash: hash ? "present" : "missing",
    });

    if (!userParam || !authDate || !hash) {
      console.warn("Missing required Telegram data fields:", {
        userParam: !!userParam,
        authDate: !!authDate,
        hash: !!hash,
        rawInitData: initData,
      });
      return null;
    }

    const user: TelegramUser = JSON.parse(userParam);

    return {
      user,
      chat_instance: urlParams.get("chat_instance") ?? undefined,
      chat_type: urlParams.get("chat_type") ?? undefined,
      auth_date: parseInt(authDate, 10),
      hash,
    };
  } catch (error) {
    console.error("Error getting Telegram Web App data:", error);
    return null;
  }
}

/**
 * Initializes Telegram Web App using @twa-dev/sdk
 * Should be called when the app loads
 */
export function initTelegramWebApp(): boolean {
  if (typeof window === "undefined") {
    return false;
  }

  try {
    console.log("Initializing Telegram Web App with @twa-dev/sdk");

    // Initialize using @twa-dev/sdk
    WebApp.ready();
    WebApp.expand();

    // Set theme colors
    WebApp.setHeaderColor("#000000");
    WebApp.setBackgroundColor("#ffffff");

    console.log("Telegram Web App initialized successfully");
    return true;
  } catch (error) {
    console.error("Error initializing Telegram Web App:", error);
    return false;
  }
}

/**
 * Checks if the app is running inside Telegram using @twa-dev/sdk
 */
export function isTelegramWebApp(): boolean {
  if (typeof window === "undefined") {
    return false;
  }

  try {
    // Check if we have access to Telegram WebApp through @twa-dev/sdk
    return !!(WebApp && WebApp.initData !== undefined);
  } catch (error) {
    console.warn("Error checking Telegram WebApp availability:", error);
    return false;
  }
}
